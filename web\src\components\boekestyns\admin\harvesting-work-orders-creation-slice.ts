import { createSlice, PayloadAction, createSelector } from '@reduxjs/toolkit';
import { RootState } from '@/services/store';
import * as models from 'api/models/boekestyns';
import { DateTime } from 'luxon';

interface WorkOrderCreationData {
  crewSize: number;
  finalRound: boolean;
  harvestingComments: string | null;
  varieties: models.HarvestingWorkOrderVariety[];
  maximumHarvestRounds: number;
  defaultExpectedHarvestPercentage: number | null;
}

interface HarvestingWorkOrdersCreationState {
  // Current order being scheduled
  selectedOrder: models.HarvestingOrder | null;
  
  // Work orders data by date
  workOrdersByDate: { [date: string]: WorkOrderCreationData };
  
  // UI state
  selectedTab: number;
  showDatePicker: boolean;
  workOrderDate: string;
  
  // Variety selection state
  selectedVarieties: { [varietyName: string]: boolean } | null;
  varietyExpectedPercentages: { [varietyName: string]: number };
}

const initialWorkOrderData: WorkOrderCreationData = {
  crewSize: 1,
  finalRound: false,
  harvestingComments: null,
  varieties: [],
  maximumHarvestRounds: 1,
  defaultExpectedHarvestPercentage: null,
};

const initialState: HarvestingWorkOrdersCreationState = {
  selectedOrder: null,
  workOrdersByDate: {
    [DateTime.now().toFormat('yyyy-MM-dd')]: { ...initialWorkOrderData }
  },
  selectedTab: 0,
  showDatePicker: false,
  workOrderDate: DateTime.now().toFormat('yyyy-MM-dd'),
  selectedVarieties: null,
  varietyExpectedPercentages: {},
};

const harvestingWorkOrdersCreationSlice = createSlice({
  name: 'harvestingWorkOrdersCreation',
  initialState,
  reducers: {
    // Order selection
    setSelectedOrder(state, action: PayloadAction<models.HarvestingOrder | null>) {
      state.selectedOrder = action.payload;
      if (action.payload) {
        // Initialize selected varieties based on order varieties
        state.selectedVarieties = action.payload.varieties.reduce(
          (acc, v) => ({ ...acc, [v.name]: true }), 
          {}
        );
      } else {
        state.selectedVarieties = null;
      }
    },

    // Work order data management
    updateWorkOrderData(state, action: PayloadAction<{ date: string; updates: Partial<WorkOrderCreationData> }>) {
      const { date, updates } = action.payload;
      if (state.workOrdersByDate[date]) {
        state.workOrdersByDate[date] = {
          ...state.workOrdersByDate[date],
          ...updates,
        };
      }
    },

    // Date management
    addWorkOrderDate(state, action: PayloadAction<string>) {
      const date = action.payload;
      if (!state.workOrdersByDate[date]) {
        state.workOrdersByDate[date] = { ...initialWorkOrderData };
      }
      state.showDatePicker = false;
      const dates = Object.keys(state.workOrdersByDate).sort();
      state.selectedTab = dates.indexOf(date);
    },

    removeWorkOrderDate(state, action: PayloadAction<string>) {
      const date = action.payload;
      const dates = Object.keys(state.workOrdersByDate);
      
      // Don't allow removing the last date
      if (dates.length > 1) {
        delete state.workOrdersByDate[date];
        
        // Adjust selected tab if necessary
        const remainingDates = Object.keys(state.workOrdersByDate).sort();
        if (state.selectedTab >= remainingDates.length) {
          state.selectedTab = remainingDates.length - 1;
        }
      }
    },

    // UI state management
    setSelectedTab(state, action: PayloadAction<number>) {
      state.selectedTab = action.payload;
    },

    setShowDatePicker(state, action: PayloadAction<boolean>) {
      state.showDatePicker = action.payload;
    },

    setWorkOrderDate(state, action: PayloadAction<string>) {
      state.workOrderDate = action.payload;
    },

    // Variety management
    setSelectedVarieties(state, action: PayloadAction<{ [varietyName: string]: boolean } | null>) {
      state.selectedVarieties = action.payload;
    },

    updateVarietySelection(state, action: PayloadAction<{ varietyName: string; selected: boolean }>) {
      const { varietyName, selected } = action.payload;
      if (state.selectedVarieties) {
        state.selectedVarieties[varietyName] = selected;
      }
    },

    setVarietyExpectedPercentages(state, action: PayloadAction<{ [varietyName: string]: number }>) {
      state.varietyExpectedPercentages = action.payload;
    },

    updateVarietyExpectedPercentage(state, action: PayloadAction<{ varietyName: string; percentage: number }>) {
      const { varietyName, percentage } = action.payload;
      state.varietyExpectedPercentages[varietyName] = percentage;
    },

    // Reset state
    resetWorkOrdersCreation(state) {
      return {
        ...initialState,
        workOrdersByDate: {
          [DateTime.now().toFormat('yyyy-MM-dd')]: { ...initialWorkOrderData }
        },
      };
    },
  },
});

export const {
  setSelectedOrder,
  updateWorkOrderData,
  addWorkOrderDate,
  removeWorkOrderDate,
  setSelectedTab,
  setShowDatePicker,
  setWorkOrderDate,
  setSelectedVarieties,
  updateVarietySelection,
  setVarietyExpectedPercentages,
  updateVarietyExpectedPercentage,
  resetWorkOrdersCreation,
} = harvestingWorkOrdersCreationSlice.actions;

// Selectors
export const selectSelectedOrder = (state: RootState) => 
  state.harvestingWorkOrdersCreation.selectedOrder;

export const selectWorkOrdersByDate = (state: RootState) => 
  state.harvestingWorkOrdersCreation.workOrdersByDate;

export const selectSelectedTab = (state: RootState) => 
  state.harvestingWorkOrdersCreation.selectedTab;

export const selectShowDatePicker = (state: RootState) => 
  state.harvestingWorkOrdersCreation.showDatePicker;

export const selectWorkOrderDate = (state: RootState) => 
  state.harvestingWorkOrdersCreation.workOrderDate;

export const selectSelectedVarieties = (state: RootState) => 
  state.harvestingWorkOrdersCreation.selectedVarieties;

export const selectVarietyExpectedPercentages = (state: RootState) => 
  state.harvestingWorkOrdersCreation.varietyExpectedPercentages;

// Computed selectors
export const selectWorkOrderDates = createSelector(
  [selectWorkOrdersByDate],
  (workOrdersByDate) => Object.keys(workOrdersByDate).sort()
);

export const selectWorkOrderForDate = createSelector(
  [selectWorkOrdersByDate, (state: RootState, date: string) => date],
  (workOrdersByDate, date) => workOrdersByDate[date] || initialWorkOrderData
);

export const selectCurrentWorkOrder = createSelector(
  [selectWorkOrdersByDate, selectWorkOrderDates, selectSelectedTab],
  (workOrdersByDate, dates, selectedTab) => {
    const currentDate = dates[selectedTab];
    return currentDate ? workOrdersByDate[currentDate] : initialWorkOrderData;
  }
);

export const selectCurrentDate = createSelector(
  [selectWorkOrderDates, selectSelectedTab],
  (dates, selectedTab) => dates[selectedTab] || DateTime.now().toFormat('yyyy-MM-dd')
);

export default harvestingWorkOrdersCreationSlice.reducer;
