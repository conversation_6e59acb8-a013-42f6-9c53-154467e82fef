import { formatNumber } from "@/utils/format";
import { HarvestingOrder, HarvestingWorkOrder, HarvestingWorkOrderVariety } from "api/models/boekestyns";
import moment from 'moment';
import { useState } from "react";
import * as HeadlessUI from '@headlessui/react';
import { classNames } from '@/utils/class-names';
import { useAppSelector, useAppDispatch } from '@/services/hooks';
import {
  updateVarietySelection,
  updateVarietyExpectedPercentage as updateVarietyPercentageAction,
  setVarietyExpectedPercentages,
  selectSelectedVarieties,
  selectVarietyExpectedPercentages,
} from './harvesting-work-orders-creation-slice';

export type HarvestingWorkOrderDayProps = {
  workOrder: Partial<HarvestingWorkOrder>;
  allWorkOrders: { [key: string]: Partial<HarvestingWorkOrder>; };
  order: HarvestingOrder | null;
  date: string;
  onUpdateWorkOrder: (date: string, updates: Partial<HarvestingWorkOrder>) => void;
};

export function HarvestingWorkOrderDay({ workOrder, allWorkOrders, order, date, onUpdateWorkOrder}: HarvestingWorkOrderDayProps) {
  const dispatch = useAppDispatch();

  // Use state from parent component instead of local state
  const crewSize = workOrder.crewSize || 1;
  const finalRound = workOrder.finalRound || false;
  const comments = workOrder.harvestingComments || null;

  const selectedVarieties = useAppSelector(selectSelectedVarieties);
  const varietyExpectedPercentages = useAppSelector(selectVarietyExpectedPercentages);
  const [defaultExpectedHarvestPercentage, setDefaultExpectedHarvestPercentage] = useState<number | null>(workOrder.defaultExpectedHarvestPercentage || null);

  // Helper functions to update work order data
  const updateCrewSize = (newCrewSize: number) => {
    onUpdateWorkOrder(date, { crewSize: newCrewSize });
  };

  const updateFinalRound = (newFinalRound: boolean) => {
    onUpdateWorkOrder(date, { finalRound: newFinalRound });
  };

  const updateComments = (newComments: string) => {
    onUpdateWorkOrder(date, { harvestingComments: newComments });
  };

  const updateVarietyExpectedPercentage = (varietyNames: string[], percentage: number) => {
    const currentVarieties = workOrder.varieties || [];
    /*const updatedVarieties = currentVarieties.map(variety =>
      variety.name === varietyName
        ? { ...variety, expectedHarvestPercentage: percentage }
        : variety
    );*/
    const updatedVarieties = varietyNames.map(varietyName => {
      const variety = currentVarieties.find(v => v.name === varietyName);
      if (variety) {
        return { ...variety, expectedHarvestPercentage: percentage };
      } else {
        return {
          id: 0, // Will be set by backend
          workOrderId: 0, // Will be set by backend
          name: varietyName,
          pots: 0,
          cases: 0,
          beginningQuantity: 0,
          expectedHarvestPercentage: percentage,
          comment: null
        };
      }
    });

    onUpdateWorkOrder(date, { varieties: updatedVarieties });
  };

  // Calculate total pots already scheduled in previous work orders based on expected percentages
  const getVarietyScheduledPots = (variety: string) => {
    const varietyData = order?.varieties.find(v => v.name === variety);
    if (!varietyData) return 0;

    return Object.keys(allWorkOrders).reduce((total, workOrderDate) => {
      if (workOrderDate >= date) return total;

      const workOrder = allWorkOrders[workOrderDate];
      if (!workOrder.varieties) return total;

      const varietyWorkOrder = workOrder.varieties.find(v => v.name === variety);
      if (!varietyWorkOrder || !varietyWorkOrder.expectedHarvestPercentage) return total;

      // Calculate scheduled pots based on expected percentage of currently available pots
      // Can't use getVarietyAvailablePots because it uses getVarietyScheduledPots in a circular dependency
      const availablePots = varietyData.pots - total;
      const scheduledPots = Math.round(availablePots * (varietyWorkOrder.expectedHarvestPercentage / 100));
      //const scheduledPots = Math.round(varietyData.pots * (varietyWorkOrder.expectedHarvestPercentage / 100));

      return total + scheduledPots;
    }, 0);
  };

  // Calculate remaining pots available for scheduling
  const getVarietyAvailablePots = (variety: string) => {
    const varietyData = order?.varieties.find(v => v.name === variety);
    if (!varietyData) return 0;

    const scheduledPots = getVarietyScheduledPots(variety);
    const availablePots = varietyData.pots - scheduledPots;

    return Math.max(0, availablePots);
  };

  // Get expected percentage for this variety
  const getExpectedHarvestPercentage = (variety: string) => {
    // Check if variety exists in work order varieties
    const varietyData = workOrder.varieties?.find(v => v.name === variety);
    if (varietyData && varietyData.expectedHarvestPercentage !== null) {
      return varietyData.expectedHarvestPercentage;
    }

    // if the date is the last date, schedule 100%
    const isLastDate = Object.keys(allWorkOrders).pop() === date;
    if (isLastDate) {
      return 100;
    }
    // Use default percentage from work order or 0
    return 0;
  };

  // Calculate pots to schedule for this round based on expected percentage
  const getVarietyToSchedule = (variety: string) => {
    const varietyData = order?.varieties.find(v => v.name === variety);
    if (!varietyData) return 0;

    const availablePots = getVarietyAvailablePots(variety);
    const expectedPercentage = varietyExpectedPercentages[variety] || getExpectedHarvestPercentage(variety);

    if (finalRound) {
      return availablePots;
    }

    const scheduledForThisRound = Math.round(availablePots * (expectedPercentage / 100));

    return Math.min(scheduledForThisRound, availablePots);
  };

  const handleSelectVarietyChanged = (checked: boolean, variety: string) => {
    dispatch(updateVarietySelection({ varietyName: variety, selected: checked }));

    if (checked && !workOrder.varieties?.find(v => v.name === variety)) {
      onUpdateWorkOrder(date, { varieties: [
        ...workOrder.varieties || [],
        {
          id: 0,
          workOrderId: 0,
          name: variety,
          pots: 0,
          cases: 0,
          beginningQuantity: getVarietyAvailablePots(variety),
          expectedHarvestPercentage: getExpectedHarvestPercentage(variety),
          comment: null
        }
      ]});
    } else if (!checked) {
      onUpdateWorkOrder(date, { varieties: workOrder.varieties?.filter(v => v.name !== variety) });
    }
  };

  const handleExpectedHarvestPercentageChange = (e: React.ChangeEvent<HTMLInputElement>, variety: string) => {
    const value = e.target.valueAsNumber;
    const percentage = isNaN(value) ? 0 : value;
    updateVarietyExpectedPercentage([variety], percentage);
    dispatch(updateVarietyPercentageAction({ varietyName: variety, percentage }));

    onUpdateWorkOrder(date, {varieties: workOrder.varieties?.map(
      v => v.name === variety ? { ...v,
        expectedHarvestPercentage: percentage,
        beginningQuantity: getVarietyAvailablePots(variety)
      } : v)
    });
  };

  const handleApplyDefaultPercentage = () => {
    const varietyNames = order?.varieties.map(v => v.name) || [];
    updateVarietyExpectedPercentage(varietyNames, defaultExpectedHarvestPercentage || 0);
    dispatch(setVarietyExpectedPercentages(
      varietyNames.reduce((acc, v) => ({ ...acc, [v]: defaultExpectedHarvestPercentage || 0 }), {})
    ));
    onUpdateWorkOrder(date, { defaultExpectedHarvestPercentage });
  };

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-4">
        Work Order for {moment(date).format('dddd, MMMM D, YYYY')}
      </h3>

        <div className="rounded border p-8">
          <div className="flex justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              {order?.orderNumber}
              <div className="italic">
                {order?.plant.name}
              </div>
            </h3>
            <div className="flex items-center">
              <label htmlFor="defaultPercentage" className="mr-2 text-sm font-medium text-gray-700">
                Default Percentage:
              </label>
              <input
                type="number"
                min="0"
                max="100"
                defaultValue={workOrder.defaultExpectedHarvestPercentage || 0}
                onChange={(e) => setDefaultExpectedHarvestPercentage(e.target.valueAsNumber)}
                className="block w-24 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
              <button onClick={handleApplyDefaultPercentage} className="ml-2 btn-secondary ">
                Apply to all
              </button>
            </div>
          </div>
          
          <div>
            <table className="min-w-full divide-y divide-gray-300 text-sm">
              <thead>
                <tr>
                  <th className="w-1 p-2">Variety</th>
                  <th className="w-1 p-2 text-right">Total Planted</th>
                  <th className="w-1 p-2 text-right">Already Scheduled</th>
                  <th className="w-1 p-2 text-right">Available</th>
                  <th className="w-1 whitespace-nowrap p-2 text-right">
                    To Schedule
                  </th>
                  <th className="w-1 p-2 text-right">Expected Percentage</th>
                  <th>&nbsp;</th>
                </tr>
              </thead>
              <tbody>
                {order?.varieties.map((variety) => (
                  <tr key={variety.name}>
                    <td className="w-1 whitespace-nowrap p-2 text-left">
                      {variety.name}
                    </td>
                    <td className="w-1 p-2 text-right">
                      {formatNumber(variety.pots, '0,0')}
                    </td>
                    <td className="w-1 p-2 text-right">
                      {formatNumber(getVarietyScheduledPots(variety.name), '0,0')}
                    </td>
                    <td className="w-1 p-2 text-right">
                      {formatNumber(getVarietyAvailablePots(variety.name), '0,0')}
                    </td>
                    <td className="w-1 p-2 text-right">
                      {formatNumber(getVarietyToSchedule(variety.name), '0,0')}
                    </td>
                    <td className="w-1 p-2 text-right">
                      <input
                        type="number"
                        value={varietyExpectedPercentages[variety.name] || getExpectedHarvestPercentage(variety.name)}
                        onChange={(e) => handleExpectedHarvestPercentageChange(e, variety.name)}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        min="0"
                        max="100"
                        step="1"
                      />
                    </td>
                    <td className="text-center">
                      <HeadlessUI.Switch
                        onChange={(checked) =>
                          handleSelectVarietyChanged(checked, variety.name)
                        }
                        checked={selectedVarieties?.[variety.name] || false}
                        className={classNames(
                          selectedVarieties?.[variety.name] ? 'bg-blue-400' : 'bg-gray-200',
                          'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                        )}
                      >
                        <span
                          aria-hidden="true"
                          className={classNames(
                            selectedVarieties?.[variety.name] ? 'translate-x-5' : 'translate-x-0',
                            'pointer-events-none inline-block size-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                          )}
                        />
                      </HeadlessUI.Switch>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="mt-4 grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-500">
                Crew Size
              </label>
              <input
                type="number"
                value={crewSize}
                onChange={(e) => updateCrewSize(e.target.valueAsNumber)}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
            <div className="col-span-3">
              <label className="block text-sm font-medium text-gray-500">
                Comments
              </label>
              <textarea
                rows={3}
                value={comments ?? ''}
                onChange={(e) => updateComments(e.target.value)}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>
      </div>
  );
}
