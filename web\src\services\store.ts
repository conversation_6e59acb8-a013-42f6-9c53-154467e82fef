import { configureStore, ThunkAction, Action } from '@reduxjs/toolkit';
import { boekestynListApi } from 'api/boekestyn-service';
import { boekestynStickingApi } from 'api/boekestyn-sticking-service';
import { boekestynSpacingApi } from 'api/boekestyn-spacing-service';
import { boekestynHarvestingApi } from 'api/boekestyn-harvesting-service';
import { futureOrdersListApi } from 'api/future-orders-service';
import { seasonsApi, prebookListApi } from 'api/prebooks-service';
import { settingsApi } from 'api/settings-service';
import { spireApi } from 'api/spire-service';
import { securityQueryApi } from 'api/security-service';
import boekestynAdmin from '@/components/boekestyns/admin/admin-slice';
import boekestynStickingAdmin from '@/components/boekestyns/admin/sticking-slice';
import boekestynSpacingAdmin from '@/components/boekestyns/admin/spacing-slice';
import boekestynHarvestingAdmin from '@/components/boekestyns/admin/harvesting-slice';
import harvestingWorkOrdersCreation from '@/components/boekestyns/admin/harvesting-work-orders-creation-slice';
import boekestynList from '@/components/boekestyns/boekestyn-list-slice';
import boekestynSales from '@/components/boekestyns/sales/sales-slice';
import boekestynSticking from '@/components/boekestyns/sticking/sticking-slice';
import boekestynSpacing from '@/components/boekestyns/spacing/spacing-slice';
import boekestynHarvesting from '@/components/boekestyns/harvesting/harvesting-slice';
import boekestynTasks from '@/components/boekestyns/tasks/task-slice';
import futureOrderCreate from '@/components/future-orders/future-order-create-slice';
import futureOrderDetail from '@/components/future-orders/future-order-detail-slice';
import futureOrderList from '@/components/future-orders/future-order-list-slice';
import addFromFutureOrder from '@/components/future-orders/add-from-order-slice';
import boekestynProducts from '@/components/future-orders/boekestyn-product-slice';
import splitOrder from '@/components/future-orders/split-order-slice';
import futureOrderUpgradeItemsList from '@/components/upgrades/upgrade-item-list-slice';
import boekestynPrebookProducts from '@/components/prebooks/boekestyn-product-slice';
import prebookDetail from '@/components/prebooks/prebook-detail-slice';
import prebookEmail from '@/components/prebooks/prebook-email-slice';
import emailBatch from '@/components/prebooks/email-batch-slice';
import prebookList from '@/components/prebooks/prebook-list-slice';
import customerSettings from '@/components/settings/customers/customer-settings-slice';
import userSettings from '@/components/settings/users/user-settings-slice';
import seasonSettings from '@/components/settings/seasons/season-settings-slice';
import productDefaultSettings from '@/components/settings/products/product-default-settings-slice';
import upgradeOptionsSettings from '@/components/settings/upgrade-options/upgrade-options-slice';
import toaster from '@/components/toaster-slice';
import productDefaultDetail from '@/components/settings/products/product-default-detail-slice';
import defaultVendorOverrides from '@/components/settings/default-vendor-overrides/default-vendor-overrides-slice';

export const store = configureStore({
  reducer: {
    boekestynSticking,
    boekestynSpacing,
    boekestynHarvesting,
    boekestynAdmin,
    boekestynStickingAdmin,
    boekestynSpacingAdmin,
    boekestynHarvestingAdmin,
    harvestingWorkOrdersCreation,
    boekestynList,
    boekestynProducts,
    splitOrder,
    boekestynPrebookProducts,
    boekestynSales,
    boekestynTasks,
    futureOrderCreate,
    futureOrderDetail,
    futureOrderList,
    addFromFutureOrder,
    futureOrderUpgradeItemsList,
    prebookDetail,
    prebookEmail,
    emailBatch,
    prebookList,
    customerSettings,
    userSettings,
    seasonSettings,
    productDefaultSettings,
    upgradeOptionsSettings,
    toaster,
    productDefaultDetail,
    defaultVendorOverrides,
    [boekestynListApi.reducerPath]: boekestynListApi.reducer,
    [boekestynStickingApi.reducerPath]: boekestynStickingApi.reducer,
    [boekestynSpacingApi.reducerPath]: boekestynSpacingApi.reducer,
    [boekestynHarvestingApi.reducerPath]: boekestynHarvestingApi.reducer,
    [futureOrdersListApi.reducerPath]: futureOrdersListApi.reducer,
    [spireApi.reducerPath]: spireApi.reducer,
    [seasonsApi.reducerPath]: seasonsApi.reducer,
    [prebookListApi.reducerPath]: prebookListApi.reducer,
    [securityQueryApi.reducerPath]: securityQueryApi.reducer,
    [settingsApi.reducerPath]: settingsApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(
      boekestynListApi.middleware,
      boekestynStickingApi.middleware,
      boekestynSpacingApi.middleware,
      boekestynHarvestingApi.middleware,
      futureOrdersListApi.middleware,
      spireApi.middleware,
      seasonsApi.middleware,
      prebookListApi.middleware,
      securityQueryApi.middleware,
      settingsApi.middleware
    ),
});

export type AppDispatch = typeof store.dispatch;
export type RootState = ReturnType<typeof store.getState>;
export type AppThunk<ReturnType = void> = ThunkAction<
  ReturnType,
  RootState,
  unknown,
  Action<string>
>;
